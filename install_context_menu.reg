Windows Registry Editor Version 5.00

; 为文件添加右键菜单项：将空格替换为下划线
[HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces]
@="将文件名空格替换为下划线"
"Icon"="shell32.dll,134"

[HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command]
@="\"d:\\work\\控制\\replace_spaces_menu.bat\" \"%1\""

; 为多选文件添加右键菜单项
[HKEY_CLASSES_ROOT\Directory\Background\shell\ReplaceSpacesMulti]
@="批量替换文件名空格为下划线"
"Icon"="shell32.dll,134"

[HKEY_CLASSES_ROOT\Directory\Background\shell\ReplaceSpacesMulti\command]
@="\"d:\\work\\控制\\replace_spaces_menu.bat\""
