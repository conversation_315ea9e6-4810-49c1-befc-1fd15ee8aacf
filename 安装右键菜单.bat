@echo off
chcp 65001 >nul
title 安装文件名空格替换右键菜单

echo ========================================
echo   文件名空格替换工具 - 右键菜单安装
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：建议以管理员身份运行此安装程序
    echo 如果安装失败，请右键选择"以管理员身份运行"
    echo.
)

REM 获取当前目录
set "CURRENT_DIR=%~dp0"
echo 当前安装目录：%CURRENT_DIR%
echo.

REM 检查必要文件是否存在
echo 检查必要文件...
set "MISSING_FILES="

if not exist "%CURRENT_DIR%replace_spaces_in_filename.py" (
    echo ✗ 缺少文件：replace_spaces_in_filename.py
    set "MISSING_FILES=1"
) else (
    echo ✓ replace_spaces_in_filename.py
)

if not exist "%CURRENT_DIR%replace_spaces_menu.bat" (
    echo ✗ 缺少文件：replace_spaces_menu.bat
    set "MISSING_FILES=1"
) else (
    echo ✓ replace_spaces_menu.bat
)

if not exist "%CURRENT_DIR%install_context_menu.reg" (
    echo ✗ 缺少文件：install_context_menu.reg
    set "MISSING_FILES=1"
) else (
    echo ✓ install_context_menu.reg
)

if defined MISSING_FILES (
    echo.
    echo 错误：缺少必要文件，无法继续安装！
    pause
    exit /b 1
)

echo.
echo 检查Python环境...

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python环境正常
    python --version
) else (
    python3 --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Python3环境正常
        python3 --version
    ) else (
        py --version >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ Python Launcher环境正常
            py --version
        ) else (
            echo ✗ 未找到Python环境！
            echo.
            echo 请先安装Python：
            echo 1. 访问 https://www.python.org/
            echo 2. 下载并安装最新版本的Python
            echo 3. 安装时勾选"Add Python to PATH"选项
            echo 4. 重新运行此安装程序
            echo.
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo 准备安装右键菜单...
echo ========================================
echo.
echo 安装内容：
echo • 单文件右键菜单：将文件名空格替换为下划线
echo • 批量处理菜单：批量替换文件名空格为下划线
echo.
echo 安装位置：%CURRENT_DIR%
echo.

set /p "CONFIRM=确认安装？(Y/N): "
if /i not "%CONFIRM%"=="Y" if /i not "%CONFIRM%"=="YES" (
    echo 安装已取消。
    pause
    exit /b 0
)

echo.
echo 正在安装右键菜单...

REM 执行注册表文件
regedit /s "%CURRENT_DIR%install_context_menu.reg"

if %errorlevel% equ 0 (
    echo ✓ 右键菜单安装成功！
    echo.
    echo ========================================
    echo 安装完成！
    echo ========================================
    echo.
    echo 使用方法：
    echo 1. 右键点击任意文件 → 选择"将文件名空格替换为下划线"
    echo 2. 在文件夹空白处右键 → 选择"批量替换文件名空格为下划线"
    echo.
    echo 注意事项：
    echo • 重命名操作不可撤销，请谨慎使用
    echo • 建议先在测试文件上验证功能
    echo • 如需卸载，请运行 uninstall_context_menu.reg
    echo.
) else (
    echo ✗ 右键菜单安装失败！
    echo.
    echo 可能的原因：
    echo 1. 权限不足 - 请以管理员身份运行
    echo 2. 注册表被保护 - 请检查安全软件设置
    echo 3. 系统限制 - 请检查组策略设置
    echo.
)

pause
