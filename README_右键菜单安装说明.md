# Windows右键菜单 - 文件名空格替换工具

## 功能说明
这个工具可以将选中文件的文件名中的空格替换为下划线（_），支持单个文件和批量处理。

## 文件说明
- `replace_spaces_in_filename.py` - 主要的Python脚本，执行重命名操作
- `replace_spaces_menu.bat` - 批处理文件，用于调用Python脚本
- `install_context_menu.reg` - 注册表文件，用于安装右键菜单
- `uninstall_context_menu.reg` - 注册表文件，用于卸载右键菜单
- `README_右键菜单安装说明.md` - 本说明文件

## 安装步骤

### 1. 确保Python环境
确保您的系统已安装Python，并且可以在命令行中使用以下命令之一：
- `python`
- `python3`
- `py`

如果没有安装Python，请从 [python.org](https://www.python.org/) 下载并安装。

### 2. 安装右键菜单
1. 右键点击 `install_context_menu.reg` 文件
2. 选择"合并"或"打开"
3. 在弹出的确认对话框中点击"是"
4. 看到"信息已成功添加到注册表中"的提示后，点击"确定"

### 3. 验证安装
安装完成后，您可以：
1. 右键点击任意文件，应该能看到"将文件名空格替换为下划线"选项
2. 在文件夹空白处右键，应该能看到"批量替换文件名空格为下划线"选项

## 使用方法

### 单个文件处理
1. 右键点击要重命名的文件
2. 选择"将文件名空格替换为下划线"
3. 程序会自动处理并显示结果

### 批量文件处理
1. 选中多个文件（按住Ctrl键点击多个文件）
2. 右键选择"将文件名空格替换为下划线"
3. 程序会批量处理所有选中的文件

### 文件夹内批量处理
1. 进入包含要处理文件的文件夹
2. 在文件夹空白处右键
3. 选择"批量替换文件名空格为下划线"
4. 程序会处理当前文件夹中的所有文件

## 功能特点
- ✅ 支持单个文件和批量文件处理
- ✅ 自动检测文件名中是否包含空格
- ✅ 防止覆盖已存在的文件
- ✅ 详细的处理结果显示
- ✅ 友好的图形界面
- ✅ 错误处理和权限检查

## 处理规则
- 只替换文件名中的空格，不影响文件扩展名
- 如果文件名中没有空格，会跳过处理
- 如果目标文件名已存在，会跳过处理以防止覆盖
- 处理结果会在GUI窗口中详细显示

## 示例
```
原文件名: "我的 文档 文件.txt"
新文件名: "我的_文档_文件.txt"

原文件名: "photo 2023 01 15.jpg"
新文件名: "photo_2023_01_15.jpg"
```

## 卸载方法
如果要卸载右键菜单：
1. 右键点击 `uninstall_context_menu.reg` 文件
2. 选择"合并"或"打开"
3. 在确认对话框中点击"是"
4. 右键菜单项将被移除

## 故障排除

### 问题1：右键菜单没有出现
- 确保已正确执行注册表文件
- 尝试重启文件资源管理器或重启电脑
- 检查注册表文件中的路径是否正确

### 问题2：点击菜单项没有反应
- 确保Python已正确安装并添加到PATH
- 检查批处理文件和Python脚本是否在正确位置
- 尝试直接运行批处理文件测试

### 问题3：权限错误
- 确保对要重命名的文件有写权限
- 尝试以管理员身份运行
- 检查文件是否被其他程序占用

## 技术说明
- 使用Python 3编写，兼容Windows系统
- 使用tkinter创建图形界面
- 通过Windows注册表添加右键菜单
- 支持中文文件名和路径

## 注意事项
- 请在使用前备份重要文件
- 建议先在测试文件上验证功能
- 重命名操作不可撤销，请谨慎使用
- 如果遇到问题，可以查看详细的错误信息

## 版本信息
- 版本：1.0
- 作者：AI助手
- 创建日期：2025年8月
- 兼容性：Windows 7/8/10/11
