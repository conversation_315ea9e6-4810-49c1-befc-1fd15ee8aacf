@echo off
title 快速安装右键菜单

echo 正在安装文件名空格替换右键菜单...
echo.

REM 获取当前目录
set "CURRENT_DIR=%~dp0"
if "%CURRENT_DIR:~-1%"=="\" set "CURRENT_DIR=%CURRENT_DIR:~0,-1%"

REM 转义路径中的反斜杠
set "ESCAPED_PATH=%CURRENT_DIR:\=\\%"

REM 生成注册表文件
echo Windows Registry Editor Version 5.00 > "%TEMP%\install_menu.reg"
echo. >> "%TEMP%\install_menu.reg"
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces] >> "%TEMP%\install_menu.reg"
echo @="将文件名空格替换为下划线" >> "%TEMP%\install_menu.reg"
echo "Icon"="shell32.dll,134" >> "%TEMP%\install_menu.reg"
echo. >> "%TEMP%\install_menu.reg"
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command] >> "%TEMP%\install_menu.reg"
echo @="\"%ESCAPED_PATH%\\replace_spaces_menu.bat\" \"%%1\"" >> "%TEMP%\install_menu.reg"

REM 安装注册表项
regedit /s "%TEMP%\install_menu.reg"

REM 清理临时文件
del "%TEMP%\install_menu.reg" >nul 2>&1

echo 安装完成！
echo.
echo 现在您可以：
echo 1. 右键点击任意文件
echo 2. 选择"将文件名空格替换为下划线"
echo.
pause
